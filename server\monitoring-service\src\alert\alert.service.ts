import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AlertEntity, AlertSeverity, AlertStatus } from './entities/alert.entity';
import { NotificationService } from '../notification/notification.service';

@Injectable()
export class AlertService {
  private readonly logger = new Logger(AlertService.name);

  constructor(
    @InjectRepository(AlertEntity)
    private readonly alertRepository: Repository<AlertEntity>,
    private readonly eventEmitter: EventEmitter2,
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * 创建告警
   */
  async createAlert(alertData: Partial<AlertEntity>): Promise<AlertEntity> {
    try {
      const alert = this.alertRepository.create({
        ...alertData,
        startTime: new Date(),
        status: AlertStatus.ACTIVE,
      });
      
      const savedAlert = await this.alertRepository.save(alert);
      
      // 触发告警创建事件
      this.eventEmitter.emit('alert.created', savedAlert);
      
      // 发送告警通知
      await this.notificationService.sendAlertNotification(savedAlert);
      
      return savedAlert;
    } catch (error) {
      this.logger.error(`创建告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有告警
   */
  async getAllAlerts(filters: {
    status?: AlertStatus;
    severity?: AlertSeverity;
    serviceId?: string;
    serviceType?: string;
    instanceId?: string;
    hostname?: string;
    startTime?: Date;
    endTime?: Date;
    page?: number;
    limit?: number;
  }): Promise<{ alerts: AlertEntity[]; total: number }> {
    try {
      const query = this.alertRepository.createQueryBuilder('alert');
      
      if (filters.status) {
        query.andWhere('alert.status = :status', { status: filters.status });
      }
      
      if (filters.severity) {
        query.andWhere('alert.severity = :severity', { severity: filters.severity });
      }
      
      if (filters.serviceId) {
        query.andWhere('alert.serviceId = :serviceId', { serviceId: filters.serviceId });
      }
      
      if (filters.serviceType) {
        query.andWhere('alert.serviceType = :serviceType', { serviceType: filters.serviceType });
      }
      
      if (filters.instanceId) {
        query.andWhere('alert.instanceId = :instanceId', { instanceId: filters.instanceId });
      }
      
      if (filters.hostname) {
        query.andWhere('alert.hostname = :hostname', { hostname: filters.hostname });
      }
      
      if (filters.startTime) {
        query.andWhere('alert.startTime >= :startTime', { startTime: filters.startTime });
      }
      
      if (filters.endTime) {
        query.andWhere('alert.startTime <= :endTime', { endTime: filters.endTime });
      }

      query.orderBy('alert.startTime', 'DESC');

      // 添加分页
      if (filters.page && filters.limit) {
        const offset = (filters.page - 1) * filters.limit;
        query.skip(offset).take(filters.limit);
      }

      const [alerts, total] = await query.getManyAndCount();

      return { alerts, total };
    } catch (error) {
      this.logger.error(`获取告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取单个告警
   */
  async getAlert(alertId: string): Promise<AlertEntity> {
    try {
      const alert = await this.alertRepository.findOne({
        where: { id: alertId },
        relations: ['rule']
      });

      if (!alert) {
        throw new Error(`未找到告警: ${alertId}`);
      }

      return alert;
    } catch (error) {
      this.logger.error(`获取告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取活跃告警
   */
  async getActiveAlerts(): Promise<AlertEntity[]> {
    const result = await this.getAllAlerts({ status: AlertStatus.ACTIVE });
    return result.alerts;
  }

  /**
   * 确认告警
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<AlertEntity> {
    try {
      const alert = await this.alertRepository.findOne({ where: { id: alertId } });
      
      if (!alert) {
        throw new Error(`未找到告警: ${alertId}`);
      }
      
      if (alert.status === AlertStatus.RESOLVED) {
        throw new Error(`告警已解决，无法确认: ${alertId}`);
      }
      
      alert.status = AlertStatus.ACKNOWLEDGED;
      alert.acknowledgedBy = acknowledgedBy;
      alert.acknowledgedAt = new Date();
      
      const updatedAlert = await this.alertRepository.save(alert);
      
      // 触发告警确认事件
      this.eventEmitter.emit('alert.acknowledged', updatedAlert);
      
      return updatedAlert;
    } catch (error) {
      this.logger.error(`确认告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 解决告警
   */
  async resolveAlert(alertId: string, resolvedBy?: string): Promise<AlertEntity> {
    try {
      const alert = await this.alertRepository.findOne({ where: { id: alertId } });
      
      if (!alert) {
        throw new Error(`未找到告警: ${alertId}`);
      }
      
      if (alert.status === AlertStatus.RESOLVED) {
        return alert;
      }
      
      alert.status = AlertStatus.RESOLVED;
      alert.resolvedBy = resolvedBy || 'system';
      alert.resolvedAt = new Date();
      alert.endTime = new Date();
      
      const updatedAlert = await this.alertRepository.save(alert);
      
      // 触发告警解决事件
      this.eventEmitter.emit('alert.resolved', updatedAlert);
      
      return updatedAlert;
    } catch (error) {
      this.logger.error(`解决告警失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取告警统计信息
   */
  async getAlertStats(): Promise<{
    total: number;
    active: number;
    acknowledged: number;
    resolved: number;
    bySeverity: Record<AlertSeverity, number>;
    byService: Record<string, number>;
  }> {
    try {
      const [total, active, acknowledged, resolved] = await Promise.all([
        this.alertRepository.count(),
        this.alertRepository.count({ where: { status: AlertStatus.ACTIVE } }),
        this.alertRepository.count({ where: { status: AlertStatus.ACKNOWLEDGED } }),
        this.alertRepository.count({ where: { status: AlertStatus.RESOLVED } }),
      ]);
      
      const bySeverityResult = await this.alertRepository
        .createQueryBuilder('alert')
        .select('alert.severity, COUNT(*) as count')
        .groupBy('alert.severity')
        .getRawMany();
      
      const byServiceResult = await this.alertRepository
        .createQueryBuilder('alert')
        .select('alert.serviceType, COUNT(*) as count')
        .groupBy('alert.serviceType')
        .getRawMany();
      
      const bySeverity: Record<AlertSeverity, number> = {
        [AlertSeverity.INFO]: 0,
        [AlertSeverity.WARNING]: 0,
        [AlertSeverity.ERROR]: 0,
        [AlertSeverity.CRITICAL]: 0,
      };
      
      for (const item of bySeverityResult) {
        bySeverity[item.severity] = parseInt(item.count, 10);
      }
      
      const byService: Record<string, number> = {};
      
      for (const item of byServiceResult) {
        byService[item.serviceType || 'unknown'] = parseInt(item.count, 10);
      }
      
      return {
        total,
        active,
        acknowledged,
        resolved,
        bySeverity,
        byService,
      };
    } catch (error) {
      this.logger.error(`获取告警统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
