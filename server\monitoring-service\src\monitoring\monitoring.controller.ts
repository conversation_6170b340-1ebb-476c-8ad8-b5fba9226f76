import { Controller, Get, Query, Param } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';

/**
 * 监控控制器
 * 提供监控数据的API接口
 */
@Controller('monitoring')
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  /**
   * 获取系统指标
   */
  @Get('metrics/system')
  async getSystemMetrics() {
    return await this.monitoringService.getSystemMetrics();
  }

  /**
   * 获取服务指标
   */
  @Get('metrics/services')
  async getServiceMetrics() {
    return await this.monitoringService.getServiceMetrics();
  }

  /**
   * 获取特定服务的指标
   */
  @Get('metrics/service/:serviceName')
  async getServiceMetricsByName(@Param('serviceName') serviceName: string) {
    return await this.monitoringService.getServiceMetricsByName(serviceName);
  }

  /**
   * 获取历史指标数据
   */
  @Get('metrics/history')
  async getHistoricalMetrics(
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('service') service?: string,
  ) {
    return await this.monitoringService.getHistoricalMetrics({
      from: from ? new Date(from) : undefined,
      to: to ? new Date(to) : undefined,
      service,
    });
  }

  /**
   * 获取实时指标
   */
  @Get('metrics/realtime')
  async getRealtimeMetrics() {
    return await this.monitoringService.getRealtimeMetrics();
  }
}
