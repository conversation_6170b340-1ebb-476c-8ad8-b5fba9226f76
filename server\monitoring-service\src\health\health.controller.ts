import { Controller, Get, Param } from '@nestjs/common';
import { HealthService } from './health.service';
import { ServiceHealthCheckService } from './service-health-check.service';

/**
 * 健康检查控制器
 * 提供系统和服务健康状态的API接口
 */
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthService: HealthService,
    private readonly serviceHealthCheckService: ServiceHealthCheckService,
  ) {}

  /**
   * 获取系统整体健康状态
   */
  @Get()
  async getOverallHealth() {
    return await this.healthService.getSystemHealth();
  }

  /**
   * 获取数据库健康状态
   */
  @Get('database')
  async getDatabaseHealth() {
    return await this.healthService.getDatabaseHealth();
  }

  /**
   * 获取所有服务健康状态
   */
  @Get('services')
  async getAllServicesHealth() {
    return await this.serviceHealthCheckService.checkAllServices();
  }

  /**
   * 获取特定服务健康状态
   */
  @Get('service/:serviceName')
  async getServiceHealth(@Param('serviceName') serviceName: string) {
    return await this.serviceHealthCheckService.checkService(serviceName);
  }

  /**
   * 获取健康检查历史
   */
  @Get('history')
  async getHealthHistory() {
    return await this.healthService.getHealthCheckHistory();
  }

  /**
   * 获取系统资源使用情况
   */
  @Get('resources')
  async getSystemResources() {
    return await this.healthService.getSystemHealth();
  }
}
