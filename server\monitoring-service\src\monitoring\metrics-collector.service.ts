import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as os from 'os';
import * as fs from 'fs';

interface ServiceInstance {
  id: string;
  serviceId: string;
  serviceType: string;
  host: string;
  port: number;
  healthCheckUrl?: string;
  metricsUrl?: string;
}

@Injectable()
export class MetricsCollectorService {
  private readonly logger = new Logger(MetricsCollectorService.name);
  private readonly serviceRegistryUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.serviceRegistryUrl = this.configService.get<string>('SERVICE_REGISTRY_URL', 'http://localhost:3000');
  }

  /**
   * 获取注册的服务列表
   */
  async getRegisteredServices(): Promise<ServiceInstance[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.serviceRegistryUrl}/registry/services?includeInstances=true`)
      );
      
      const services: ServiceInstance[] = [];
      
      for (const service of response.data) {
        for (const instance of service.instances) {
          services.push({
            id: instance.id,
            serviceId: service.id,
            serviceType: service.type,
            host: instance.host,
            port: instance.port,
            healthCheckUrl: instance.healthCheckUrl,
            metricsUrl: instance.metricsUrl || `http://${instance.host}:${instance.port}/monitoring/metrics`,
          });
        }
      }
      
      return services;
    } catch (error) {
      this.logger.error(`获取注册服务列表失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 收集服务指标
   */
  async collectServiceMetrics(service: ServiceInstance): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(service.metricsUrl, { timeout: 5000 })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error(`收集服务 ${service.id} 指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics(): Promise<any> {
    try {
      // CPU使用率
      const cpuUsage = await this.getCpuUsage();
      
      // 内存使用情况
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const memoryUsage = (totalMemory - freeMemory) / totalMemory;
      
      // 磁盘使用情况 (简化版本)
      const diskUsage = await this.getDiskUsage();
      
      // 负载平均值
      const loadAverage = os.loadavg();
      
      // 网络连接数
      const networkConnections = await this.getNetworkConnections();
      
      return {
        hostname: os.hostname(),
        cpuUsage,
        memoryUsage,
        totalMemory,
        freeMemory,
        diskUsage: diskUsage.usage,
        totalDisk: diskUsage.total,
        freeDisk: diskUsage.free,
        loadAverage,
        networkConnections,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`收集系统指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.getCpuInfo();
      
      // 等待100ms再次测量
      setTimeout(() => {
        const endMeasure = this.getCpuInfo();
        
        let totalDifference = 0;
        let idleDifference = 0;
        
        for (let i = 0; i < startMeasure.length; i++) {
          const startCpu = startMeasure[i];
          const endCpu = endMeasure[i];
          
          const totalStart = Object.values(startCpu).reduce((a, b) => a + b, 0);
          const totalEnd = Object.values(endCpu).reduce((a, b) => a + b, 0);
          
          totalDifference += (totalEnd - totalStart);
          idleDifference += (endCpu.idle - startCpu.idle);
        }
        
        const cpuUsage = 1 - (idleDifference / totalDifference);
        resolve(cpuUsage);
      }, 100);
    });
  }

  /**
   * 获取CPU信息
   */
  private getCpuInfo(): { user: number; nice: number; sys: number; idle: number; irq: number }[] {
    const cpus = os.cpus();
    return cpus.map(cpu => cpu.times);
  }

  /**
   * 获取磁盘使用情况 (简化版本)
   */
  private async getDiskUsage(): Promise<{ usage: number; total: number; free: number }> {
    try {
      // 简化的磁盘使用情况检查
      const stats = await fs.promises.statfs ? fs.promises.statfs('.') : null;

      if (stats) {
        const total = stats.bavail * stats.bsize;
        const free = stats.bfree * stats.bsize;
        const usage = (total - free) / total;

        return { usage, total, free };
      } else {
        // 如果无法获取真实数据，返回模拟数据
        const total = 1000000000000; // 1TB
        const free = 500000000000;   // 500GB
        const usage = (total - free) / total;

        return { usage, total, free };
      }
    } catch (error) {
      this.logger.error(`获取磁盘使用情况失败: ${error.message}`, error.stack);
      // 返回默认值
      return { usage: 0.5, total: 1000000000000, free: 500000000000 };
    }
  }

  /**
   * 获取网络连接数
   */
  private async getNetworkConnections(): Promise<number> {
    try {
      // 这里需要根据操作系统类型使用不同的命令
      // 此处简化处理，返回一个模拟值
      return Math.floor(Math.random() * 1000);
    } catch (error) {
      this.logger.error(`获取网络连接数失败: ${error.message}`, error.stack);
      return 0;
    }
  }
}
